#!/usr/bin/env python3
"""
MongoDB Date Validator Script

This script identifies MongoDB records containing invalid date fields that cause
PyMongo DateOverflowError exceptions. It scans collections to find dates with
years 0000 or greater than 9999, which are not supported by Python datetime.

Author: Generated for MongoDB Date Validation
Date: 2025-07-01
"""

import asyncio
import csv
import logging
import sys
from datetime import datetime, timezone
from typing import Dict, List, Tuple, Any, Optional, Set
from dataclasses import dataclass
from pathlib import Path
import json
import argparse
import requests
import subprocess

from pymongo import MongoClient
from pymongo.errors import ServerSelectionTimeoutError, OperationFailure
from bson.codec_options import CodecOptions, DatetimeConversion
from bson import ObjectId
from bson.errors import InvalidDocument
from bson.datetime_ms import DatetimeMS
import motor.motor_asyncio


@dataclass
class InvalidDateRecord:
    """Data class to store information about invalid date records."""
    database_name: str
    collection_name: str
    record_id: str
    field_name: str
    invalid_date_value: str
    error_type: str = "DateOverflowError"


@dataclass
class ValidationConfig:
    """Configuration for the MongoDB date validation process."""
    connection_string: str
    batch_size: int = 1000
    max_concurrent_collections: int = 10
    output_file: str = "invalid_dates_report.csv"
    log_level: str = "INFO"
    databases_to_scan: Optional[List[str]] = None
    collections_to_exclude: Optional[List[str]] = None
    sample_limit: int = 10  # Number of sample records to collect per collection


class MongoDateValidator:
    """
    Main class for validating MongoDB date fields and identifying problematic records.

    This class implements an async/await multithreading approach with separate
    processing for each tenant/collection to efficiently scan large MongoDB databases.
    """

    def __init__(self, config: ValidationConfig):
        self.config = config
        self.client: Optional[motor.motor_asyncio.AsyncIOMotorClient] = None
        self.invalid_records: List[InvalidDateRecord] = []
        self.collection_stats: Dict[str, Dict[str, int]] = {}
        self.logger = self._setup_logging()

    def _setup_logging(self) -> logging.Logger:
        """Set up logging configuration."""
        logger = logging.getLogger(__name__)
        logger.setLevel(getattr(logging, self.config.log_level.upper()))

        # Create console handler
        handler = logging.StreamHandler(sys.stdout)
        handler.setLevel(getattr(logging, self.config.log_level.upper()))

        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)

        # Add handler to logger
        if not logger.handlers:
            logger.addHandler(handler)

        return logger

    async def connect_to_mongodb(self) -> bool:
        """Establish connection to MongoDB."""
        try:
            self.client = motor.motor_asyncio.AsyncIOMotorClient(
                self.config.connection_string,
                serverSelectionTimeoutMS=5000
            )
            # Test the connection
            await self.client.admin.command('ping')
            self.logger.info("Successfully connected to MongoDB")
            return True
        except Exception as e:
            self.logger.error(f"Failed to connect to MongoDB: {e}")
            return False

    async def disconnect_from_mongodb(self):
        """Close MongoDB connection."""
        if self.client:
            self.client.close()
            self.logger.info("Disconnected from MongoDB")

    async def get_databases_and_collections(self) -> Dict[str, List[str]]:
        """Get list of databases and their collections to scan."""
        db_collections = {}

        try:
            # Get list of databases
            if self.config.databases_to_scan:
                databases = self.config.databases_to_scan
            else:
                db_list = await self.client.list_database_names()
                # Filter out system databases
                databases = [db for db in db_list if db not in ['admin', 'local', 'config']]

            for db_name in databases:
                db = self.client[db_name]
                collections = await db.list_collection_names()

                # Filter out excluded collections
                if self.config.collections_to_exclude:
                    collections = [
                        col for col in collections
                        if col not in self.config.collections_to_exclude
                    ]

                db_collections[db_name] = collections
                self.logger.info(f"Database '{db_name}': {len(collections)} collections to scan")

            return db_collections

        except Exception as e:
            self.logger.error(f"Error getting databases and collections: {e}")
            return {}

    def _is_potential_date_field(self, field_name: str, value: Any) -> bool:
        """
        Check if a field is potentially a date field based on name patterns and value type.
        """
        date_field_patterns = [
            'date', 'time', 'created', 'updated', 'modified', 'timestamp',
            'expires', 'start', 'end', 'last', 'first', '_at', '_on'
        ]

        field_lower = field_name.lower()
        is_date_named = any(pattern in field_lower for pattern in date_field_patterns)

        # Check if value is a datetime object or looks like a date
        is_date_value = isinstance(value, (datetime, DatetimeMS))

        return is_date_named or is_date_value

    def _extract_date_fields(self, document: Dict[str, Any], prefix: str = "") -> Dict[str, Any]:
        """
        Recursively extract potential date fields from a document.
        """
        date_fields = {}

        for key, value in document.items():
            full_key = f"{prefix}.{key}" if prefix else key

            if isinstance(value, dict):
                # Recursively check nested documents
                nested_dates = self._extract_date_fields(value, full_key)
                date_fields.update(nested_dates)
            elif isinstance(value, list) and value:
                # Check first element of arrays for date patterns
                if isinstance(value[0], dict):
                    nested_dates = self._extract_date_fields(value[0], f"{full_key}[0]")
                    date_fields.update(nested_dates)
                elif self._is_potential_date_field(key, value[0]):
                    date_fields[full_key] = value[0]
            elif self._is_potential_date_field(key, value):
                date_fields[full_key] = value

        return date_fields

    def _validate_date_value(self, value: Any, field_name: str) -> Tuple[bool, str]:
        """
        Validate a date value and return whether it's invalid and the reason.

        Returns:
            Tuple[bool, str]: (is_invalid, reason)
        """
        try:
            if isinstance(value, datetime):
                # Check if year is out of range
                if value.year == 0 or value.year > 9999:
                    return True, f"Year {value.year} out of range (0000 or >9999)"
                return False, ""

            elif isinstance(value, DatetimeMS):
                # DatetimeMS can handle out-of-range dates, so we need to check manually
                try:
                    # Try to convert to regular datetime to see if it would overflow
                    dt = value.as_datetime()
                    if dt.year == 0 or dt.year > 9999:
                        return True, f"Year {dt.year} out of range (0000 or >9999)"
                except (ValueError, OverflowError) as e:
                    return True, f"DatetimeMS conversion error: {str(e)}"
                return False, ""

            elif isinstance(value, str):
                # Try to parse string dates
                try:
                    # Common ISO format parsing
                    if 'T' in value or '-' in value:
                        dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
                        if dt.year == 0 or dt.year > 9999:
                            return True, f"Year {dt.year} out of range (0000 or >9999)"
                except (ValueError, OverflowError):
                    return True, "Invalid date string format"
                return False, ""

            else:
                # For other types, we can't validate as dates
                return False, ""

        except Exception as e:
            return True, f"Validation error: {str(e)}"

    async def _validate_document_dates(
        self,
        document: Dict[str, Any],
        db_name: str,
        collection_name: str
    ) -> List[InvalidDateRecord]:
        """
        Validate all date fields in a single document.
        """
        invalid_records = []

        try:
            # Extract potential date fields
            date_fields = self._extract_date_fields(document)

            for field_name, field_value in date_fields.items():
                is_invalid, reason = self._validate_date_value(field_value, field_name)

                if is_invalid:
                    invalid_record = InvalidDateRecord(
                        database_name=db_name,
                        collection_name=collection_name,
                        record_id=str(document.get('_id', 'Unknown')),
                        field_name=field_name,
                        invalid_date_value=str(field_value),
                        error_type=reason
                    )
                    invalid_records.append(invalid_record)

        except Exception as e:
            self.logger.error(f"Error validating document {document.get('_id')}: {e}")

        return invalid_records

    async def _process_collection_batch(
        self,
        collection,
        db_name: str,
        collection_name: str,
        skip: int
    ) -> Tuple[List[InvalidDateRecord], int]:
        """
        Process a batch of documents from a collection using the try-catch approach.
        """
        invalid_records = []
        processed_count = 0

        try:
            # First, try normal cursor approach to trigger DateOverflowError
            cursor = collection.find().skip(skip).limit(self.config.batch_size)

            try:
                # Attempt to iterate through documents normally
                async for document in cursor:
                    processed_count += 1
                    # Validate date fields in the document
                    doc_invalid_records = await self._validate_document_dates(
                        document, db_name, collection_name
                    )
                    invalid_records.extend(doc_invalid_records)

            except Exception as e:
                # If we get DateOverflowError or similar, use DatetimeMS approach
                if "overflow" in str(e).lower() or "out of range" in str(e).lower():
                    self.logger.warning(
                        f"DateOverflowError detected in {db_name}.{collection_name}, "
                        f"using DatetimeMS fallback for batch starting at {skip}"
                    )

                    # Use raw find with DatetimeMS codec
                    cursor_raw = collection.find(
                        {},
                        codec_options=CodecOptions(
                            datetime_conversion=DatetimeConversion.DATETIME_MS
                        )
                    ).skip(skip).limit(self.config.batch_size)

                    async for document in cursor_raw:
                        processed_count += 1
                        doc_invalid_records = await self._validate_document_dates(
                            document, db_name, collection_name
                        )
                        invalid_records.extend(doc_invalid_records)
                else:
                    # Re-raise if it's not a date overflow error
                    raise e

        except Exception as e:
            self.logger.error(
                f"Error processing batch in {db_name}.{collection_name} "
                f"(skip={skip}): {e}"
            )

        return invalid_records, processed_count

    async def _process_collection(self, db_name: str, collection_name: str) -> Dict[str, Any]:
        """
        Process a single collection to find invalid date records.
        This implements the async/await approach for separate tenant processing.
        """
        collection_stats = {
            'total_documents': 0,
            'processed_documents': 0,
            'invalid_records_count': 0,
            'sample_invalid_records': []
        }

        try:
            db = self.client[db_name]
            collection = db[collection_name]

            # Get total document count
            total_docs = await collection.count_documents({})
            collection_stats['total_documents'] = total_docs

            self.logger.info(
                f"Processing collection {db_name}.{collection_name} "
                f"({total_docs} documents)"
            )

            if total_docs == 0:
                return collection_stats

            # Process collection in batches
            skip = 0
            collection_invalid_records = []

            while skip < total_docs:
                batch_invalid_records, batch_processed = await self._process_collection_batch(
                    collection, db_name, collection_name, skip
                )

                collection_invalid_records.extend(batch_invalid_records)
                collection_stats['processed_documents'] += batch_processed

                # Log progress
                if skip % (self.config.batch_size * 10) == 0:
                    self.logger.info(
                        f"Progress {db_name}.{collection_name}: "
                        f"{collection_stats['processed_documents']}/{total_docs} documents processed, "
                        f"{len(collection_invalid_records)} invalid records found"
                    )

                skip += self.config.batch_size

            # Store results
            collection_stats['invalid_records_count'] = len(collection_invalid_records)
            collection_stats['sample_invalid_records'] = collection_invalid_records[:self.config.sample_limit]

            # Add to global invalid records list
            self.invalid_records.extend(collection_invalid_records)

            self.logger.info(
                f"Completed {db_name}.{collection_name}: "
                f"{collection_stats['processed_documents']} documents processed, "
                f"{collection_stats['invalid_records_count']} invalid records found"
            )

        except Exception as e:
            self.logger.error(f"Error processing collection {db_name}.{collection_name}: {e}")
            collection_stats['error'] = str(e)

        return collection_stats

    async def _process_database_collections(self, db_name: str, collections: List[str]) -> Dict[str, Any]:
        """
        Process all collections in a database using concurrent processing.
        """
        self.logger.info(f"Starting processing for database '{db_name}' with {len(collections)} collections")

        # Create semaphore to limit concurrent collection processing
        semaphore = asyncio.Semaphore(self.config.max_concurrent_collections)

        async def process_with_semaphore(collection_name: str):
            async with semaphore:
                return await self._process_collection(db_name, collection_name)

        # Process collections concurrently
        tasks = [process_with_semaphore(col) for col in collections]
        collection_results = await asyncio.gather(*tasks, return_exceptions=True)

        # Compile database statistics
        db_stats = {
            'total_collections': len(collections),
            'processed_collections': 0,
            'total_documents': 0,
            'total_invalid_records': 0,
            'collections': {}
        }

        for i, result in enumerate(collection_results):
            collection_name = collections[i]

            if isinstance(result, Exception):
                self.logger.error(f"Exception processing {db_name}.{collection_name}: {result}")
                db_stats['collections'][collection_name] = {'error': str(result)}
            else:
                db_stats['processed_collections'] += 1
                db_stats['total_documents'] += result.get('total_documents', 0)
                db_stats['total_invalid_records'] += result.get('invalid_records_count', 0)
                db_stats['collections'][collection_name] = result

        self.logger.info(
            f"Completed database '{db_name}': "
            f"{db_stats['processed_collections']}/{db_stats['total_collections']} collections processed, "
            f"{db_stats['total_invalid_records']} total invalid records found"
        )

        return db_stats

    async def validate_all_databases(self) -> Dict[str, Any]:
        """
        Main method to validate all databases and collections.
        """
        self.logger.info("Starting MongoDB date validation process")

        # Connect to MongoDB
        if not await self.connect_to_mongodb():
            raise Exception("Failed to connect to MongoDB")

        try:
            # Get databases and collections to scan
            db_collections = await self.get_databases_and_collections()

            if not db_collections:
                self.logger.warning("No databases or collections found to scan")
                return {}

            # Process each database
            validation_results = {}
            total_databases = len(db_collections)

            for i, (db_name, collections) in enumerate(db_collections.items(), 1):
                self.logger.info(f"Processing database {i}/{total_databases}: {db_name}")

                db_stats = await self._process_database_collections(db_name, collections)
                validation_results[db_name] = db_stats
                self.collection_stats[db_name] = db_stats

            # Generate summary
            total_invalid_records = len(self.invalid_records)
            total_collections = sum(stats['total_collections'] for stats in validation_results.values())
            total_documents = sum(stats['total_documents'] for stats in validation_results.values())

            summary = {
                'total_databases_scanned': len(validation_results),
                'total_collections_scanned': total_collections,
                'total_documents_scanned': total_documents,
                'total_invalid_records_found': total_invalid_records,
                'databases': validation_results
            }

            self.logger.info(
                f"Validation completed: {total_databases} databases, "
                f"{total_collections} collections, {total_documents} documents scanned. "
                f"Found {total_invalid_records} invalid date records."
            )

            return summary

        finally:
            await self.disconnect_from_mongodb()

    def save_results_to_csv(self, filename: Optional[str] = None) -> str:
        """
        Save validation results to CSV file in the required format:
        Database Name, Collection Name, RecordId, Field Name, Invalid Date Value
        """
        output_file = filename or self.config.output_file

        try:
            with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    'Database Name',
                    'Collection Name',
                    'RecordId',
                    'Field Name',
                    'Invalid Date Value',
                    'Error Type'
                ]
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                # Write header
                writer.writeheader()

                # Write invalid records
                for record in self.invalid_records:
                    writer.writerow({
                        'Database Name': record.database_name,
                        'Collection Name': record.collection_name,
                        'RecordId': record.record_id,
                        'Field Name': record.field_name,
                        'Invalid Date Value': record.invalid_date_value,
                        'Error Type': record.error_type
                    })

            self.logger.info(f"Results saved to {output_file}")
            return output_file

        except Exception as e:
            self.logger.error(f"Error saving results to CSV: {e}")
            raise

    def save_summary_report(self, summary: Dict[str, Any], filename: Optional[str] = None) -> str:
        """
        Save a detailed summary report as JSON.
        """
        if filename is None:
            base_name = Path(self.config.output_file).stem
            filename = f"{base_name}_summary.json"

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(summary, f, indent=2, default=str)

            self.logger.info(f"Summary report saved to {filename}")
            return filename

        except Exception as e:
            self.logger.error(f"Error saving summary report: {e}")
            raise

    def print_summary(self, summary: Dict[str, Any]):
        """
        Print a human-readable summary of the validation results.
        """
        print("\n" + "="*80)
        print("MONGODB DATE VALIDATION SUMMARY")
        print("="*80)

        print(f"Total Databases Scanned: {summary['total_databases_scanned']}")
        print(f"Total Collections Scanned: {summary['total_collections_scanned']}")
        print(f"Total Documents Scanned: {summary['total_documents_scanned']:,}")
        print(f"Total Invalid Records Found: {summary['total_invalid_records_found']:,}")

        if summary['total_invalid_records_found'] > 0:
            print(f"\nInvalid Records Rate: {(summary['total_invalid_records_found'] / summary['total_documents_scanned'] * 100):.4f}%")

        print("\nPer Database Summary:")
        print("-" * 50)

        for db_name, db_stats in summary['databases'].items():
            print(f"\nDatabase: {db_name}")
            print(f"  Collections: {db_stats['processed_collections']}/{db_stats['total_collections']}")
            print(f"  Documents: {db_stats['total_documents']:,}")
            print(f"  Invalid Records: {db_stats['total_invalid_records']:,}")

            if db_stats['total_invalid_records'] > 0:
                print(f"  Top Collections with Issues:")
                # Sort collections by invalid record count
                sorted_collections = sorted(
                    [(name, stats) for name, stats in db_stats['collections'].items()
                     if isinstance(stats, dict) and stats.get('invalid_records_count', 0) > 0],
                    key=lambda x: x[1].get('invalid_records_count', 0),
                    reverse=True
                )

                for col_name, col_stats in sorted_collections[:5]:  # Top 5
                    print(f"    {col_name}: {col_stats['invalid_records_count']} invalid records")

        print("\n" + "="*80)


def create_config_from_args(args) -> ValidationConfig:
    """Create ValidationConfig from command line arguments and API configuration."""
    config_data = {}

    # If tenant and dealer_id are provided, fetch config from API
    if hasattr(args, 'tenant') and hasattr(args, 'dealer_id') and args.tenant and args.dealer_id:
        auth_token = getattr(args, 'auth_token', 'ved')
        config_data = fetch_config_from_api(args.tenant, args.dealer_id, auth_token)
        print(f"Configuration fetched from API for tenant: {args.tenant}, dealer: {args.dealer_id}")

    # Fallback to config file if API fetch is not used or fails
    elif hasattr(args, 'config') and args.config:
        if args.config != 'config.json':
            config_data = load_config_from_file(args.config)
        else:
            config_data = load_config_from_file()
        print(f"Configuration loaded from file: {args.config}")

    # Command line arguments take precedence over config data (API or file)
    connection_string = getattr(args, 'connection_string', None) or config_data.get('connection_string')
    if not connection_string:
        if hasattr(args, 'tenant') and args.tenant:
            raise ValueError("Failed to get connection string from API. Please check tenant, dealer_id, and auth_token.")
        else:
            raise ValueError("Connection string must be provided either via --connection-string argument, API (--tenant and --dealer-id), or in config file")

    return ValidationConfig(
        connection_string=connection_string,
        batch_size=getattr(args, 'batch_size', None) or config_data.get('batch_size', 1000),
        max_concurrent_collections=getattr(args, 'max_concurrent', None) or config_data.get('max_concurrent_collections', 10),
        output_file=getattr(args, 'output_file', None) or config_data.get('output_file', 'invalid_dates_report.csv'),
        log_level=getattr(args, 'log_level', None) or config_data.get('log_level', 'INFO'),
        databases_to_scan=args.databases.split(',') if hasattr(args, 'databases') and args.databases else config_data.get('databases_to_scan'),
        collections_to_exclude=args.exclude_collections.split(',') if hasattr(args, 'exclude_collections') and args.exclude_collections else config_data.get('collections_to_exclude', 'system.users,system.roles,system.indexes').split(','),
        sample_limit=getattr(args, 'sample_limit', None) or config_data.get('sample_limit', 10)
    )


def test_api_connection(tenant: str, dealer_id: str, auth_token: str = "ved") -> bool:
    """Test API connection and print response for debugging."""
    api_url = f"http://internal-prod-dms-pvt-1426699881.us-west-1.elb.amazonaws.com/config/v2/read/secondary/MONGO/DSE_DEFAULT/{tenant}/{dealer_id}"

    try:
        print(f"Testing API connection...")
        print(f"URL: {api_url}")
        print(f"Auth Token: {auth_token}")

        response = requests.get(
            api_url,
            params={"authToken": auth_token},
            timeout=30
        )

        print(f"Response Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")

        if response.status_code == 200:
            try:
                data = response.json()
                print(f"Response Data: {json.dumps(data, indent=2)}")
                return True
            except json.JSONDecodeError:
                print(f"Response Text: {response.text}")
                return False
        else:
            print(f"Error Response: {response.text}")
            return False

    except Exception as e:
        print(f"API connection test failed: {e}")
        return False


def fetch_config_from_api(tenant: str, dealer_id: str, auth_token: str = "ved") -> dict:
    """Fetch MongoDB configuration from API endpoint."""
    api_url = f"http://internal-prod-dms-pvt-1426699881.us-west-1.elb.amazonaws.com/config/v2/read/secondary/MONGO/DSE_DEFAULT/{tenant}/{dealer_id}"

    try:
        # Make the API request
        response = requests.get(
            api_url,
            params={"authToken": auth_token},
            timeout=30
        )
        response.raise_for_status()

        config_data = response.json()

        # Extract MongoDB connection details from the API response
        # Assuming the API returns MongoDB connection details in a specific format
        # You may need to adjust this based on the actual API response structure
        if 'connectionString' in config_data:
            return {
                'connection_string': config_data['connectionString'],
                'databases': config_data.get('databases'),
                'exclude_collections': config_data.get('excludeCollections', 'system.users,system.roles,system.indexes'),
                'batch_size': config_data.get('batchSize', 1000),
                'max_concurrent_collections': config_data.get('maxConcurrentCollections', 10),
                'output_file': config_data.get('outputFile', f'invalid_dates_report_{tenant}_{dealer_id}.csv'),
                'sample_limit': config_data.get('sampleLimit', 10),
                'log_level': config_data.get('logLevel', 'INFO'),
                'no_summary': config_data.get('noSummary', False),
                'save_summary': config_data.get('saveSummary', False)
            }
        else:
            # If the API response doesn't have the expected format,
            # try to construct connection string from individual components
            host = config_data.get('host', 'localhost')
            port = config_data.get('port', 27017)
            username = config_data.get('username', '')
            password = config_data.get('password', '')
            database = config_data.get('database', '')

            if username and password:
                connection_string = f"mongodb://{username}:{password}@{host}:{port}/{database}"
            else:
                connection_string = f"mongodb://{host}:{port}"

            return {
                'connection_string': connection_string,
                'databases': config_data.get('databases'),
                'exclude_collections': 'system.users,system.roles,system.indexes',
                'batch_size': 1000,
                'max_concurrent_collections': 10,
                'output_file': f'invalid_dates_report_{tenant}_{dealer_id}.csv',
                'sample_limit': 10,
                'log_level': 'INFO',
                'no_summary': False,
                'save_summary': False
            }

    except requests.exceptions.RequestException as e:
        print(f"Error fetching configuration from API: {e}")
        print(f"API URL: {api_url}")
        raise ValueError(f"Failed to fetch configuration from API: {e}")
    except json.JSONDecodeError as e:
        print(f"Error parsing API response: {e}")
        raise ValueError(f"Invalid JSON response from API: {e}")
    except Exception as e:
        print(f"Unexpected error fetching configuration: {e}")
        raise ValueError(f"Failed to fetch configuration: {e}")


def load_config_from_file(config_file: str = "config.json") -> dict:
    """Load configuration from JSON file (fallback method)."""
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        return config_data
    except FileNotFoundError:
        print(f"Config file '{config_file}' not found. Using default values.")
        return {}
    except json.JSONDecodeError as e:
        print(f"Error parsing config file '{config_file}': {e}")
        return {}


def setup_argument_parser() -> argparse.ArgumentParser:
    """Set up command line argument parser with API and config file support."""
    parser = argparse.ArgumentParser(
        description="MongoDB Date Validator - Identify records with invalid date fields",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Fetch configuration from API using tenant and dealer ID
  python mongo_date_validator.py --tenant "your_tenant" --dealer-id "your_dealer_id"

  # Fetch configuration from API with custom auth token
  python mongo_date_validator.py --tenant "your_tenant" --dealer-id "your_dealer_id" --auth-token "custom_token"

  # Use custom config file (fallback method)
  python mongo_date_validator.py --config custom_config.json

  # Override API/config with command line arguments
  python mongo_date_validator.py --tenant "your_tenant" --dealer-id "your_dealer_id" --batch-size 2000

  # Direct connection string (bypass API and config file)
  python mongo_date_validator.py --connection-string "mongodb://localhost:27017"
        """
    )

    # API Configuration Arguments (Primary method)
    parser.add_argument(
        '--tenant',
        help='Tenant ID for API configuration fetch'
    )

    parser.add_argument(
        '--dealer-id',
        help='Dealer ID for API configuration fetch'
    )

    parser.add_argument(
        '--auth-token',
        default='ved',
        help='Authentication token for API requests (default: ved)'
    )

    # Config File Arguments (Fallback method)
    parser.add_argument(
        '--config',
        default='config.json',
        help='Path to configuration JSON file (fallback if API not used)'
    )

    # Direct Configuration Arguments (Override API/config file)
    parser.add_argument(
        '--connection-string',
        help='MongoDB connection string (e.g., "mongodb://localhost:27017") - overrides API/config'
    )

    parser.add_argument(
        '--databases',
        help='Comma-separated list of databases to scan (default: all non-system databases)'
    )

    parser.add_argument(
        '--exclude-collections',
        help='Comma-separated list of collection names to exclude from scanning'
    )

    parser.add_argument(
        '--batch-size',
        type=int,
        default=1000,
        help='Number of documents to process in each batch (default: 1000)'
    )

    parser.add_argument(
        '--max-concurrent',
        type=int,
        default=10,
        help='Maximum number of collections to process concurrently (default: 10)'
    )

    parser.add_argument(
        '--output-file',
        default='invalid_dates_report.csv',
        help='Output CSV file name (default: invalid_dates_report.csv)'
    )

    parser.add_argument(
        '--sample-limit',
        type=int,
        default=10,
        help='Number of sample invalid records to collect per collection (default: 10)'
    )

    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Logging level (default: INFO)'
    )

    parser.add_argument(
        '--no-summary',
        action='store_true',
        default=False,
        help='Skip printing summary to console'
    )

    parser.add_argument(
        '--save-summary',
        action='store_true',
        default=False,
        help='Save detailed summary report as JSON file'
    )

    parser.add_argument(
        '--test-api',
        action='store_true',
        help='Test API connection and print response (requires --tenant and --dealer-id)'
    )

    return parser


async def main():
    """Main execution function."""
    parser = setup_argument_parser()
    args = parser.parse_args()

    # Handle API connection test
    if args.test_api:
        if not args.tenant or not args.dealer_id:
            print("ERROR: --test-api requires both --tenant and --dealer-id arguments")
            sys.exit(1)

        print("Testing API connection...")
        success = test_api_connection(args.tenant, args.dealer_id, args.auth_token)
        if success:
            print("✓ API connection test successful!")
            sys.exit(0)
        else:
            print("✗ API connection test failed!")
            sys.exit(1)

    # Create configuration
    config = create_config_from_args(args)

    # Create validator instance
    validator = MongoDateValidator(config)

    try:
        # Run validation
        print("Starting MongoDB Date Validation...")
        print(f"Connection: {config.connection_string}")
        print(f"Batch Size: {config.batch_size}")
        print(f"Max Concurrent Collections: {config.max_concurrent_collections}")
        print(f"Output File: {config.output_file}")

        # Show configuration source
        if hasattr(args, 'tenant') and args.tenant:
            print(f"Configuration Source: API (Tenant: {args.tenant}, Dealer: {args.dealer_id})")
        elif hasattr(args, 'config') and args.config:
            print(f"Configuration Source: File ({args.config})")
        else:
            print("Configuration Source: Command line arguments")

        print("-" * 50)

        summary = await validator.validate_all_databases()

        # Save results to CSV
        csv_file = validator.save_results_to_csv()
        print(f"\nResults saved to: {csv_file}")

        # Save summary report if requested
        if args.save_summary:
            summary_file = validator.save_summary_report(summary)
            print(f"Summary report saved to: {summary_file}")

        # Print summary unless disabled
        if not args.no_summary:
            validator.print_summary(summary)

        # Exit with appropriate code
        if summary['total_invalid_records_found'] > 0:
            print(f"\nWARNING: Found {summary['total_invalid_records_found']} invalid date records!")
            print(f"Check the output file: {csv_file}")
            sys.exit(1)
        else:
            print("\nSUCCESS: No invalid date records found!")
            sys.exit(0)

    except KeyboardInterrupt:
        print("\nValidation interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\nERROR: {e}")
        validator.logger.exception("Validation failed with exception")
        sys.exit(1)


if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())